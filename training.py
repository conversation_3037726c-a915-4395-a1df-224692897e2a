from stable_baselines3 import PPO
from soak_env import SoakOverflowEnv
import os
import shutil

def create_rl_bot_executable(model_path, bot_name, version=0):
    """Create an executable bot that uses the trained RL model"""

    # Create the bot script content
    bot_script = f'''#!/usr/bin/env python3
import sys
import os
import numpy as np
import random

# Try to import stable_baselines3, fallback to random if not available
try:
    from stable_baselines3 import PPO
    MODEL_AVAILABLE = True
except ImportError:
    MODEL_AVAILABLE = False
    print("Warning: stable_baselines3 not available, using random actions", file=sys.stderr)

def main():
    # Read initial game setup
    my_id = int(input())
    agent_data_count = int(input())

    my_agent_ids = []
    for _ in range(agent_data_count):
        line = input().split()
        agent_id, player_id = int(line[0]), int(line[1])
        if player_id == my_id:
            my_agent_ids.append(agent_id)

    width, height = map(int, input().split())

    # Read map data
    for _ in range(width * height):
        input()  # x, y, type

    # Load model if available
    model = None
    if MODEL_AVAILABLE and os.path.exists("{model_path}.zip"):
        try:
            model = PPO.load("{model_path}")
        except Exception as e:
            print(f"Error loading model: {{e}}", file=sys.stderr)
            model = None

    # Game loop
    step_count = 0
    while True:
        try:
            agent_count = int(input())

            # Read agent states
            agents = []
            for _ in range(agent_count):
                line = input().split()
                agent_data = {{
                    'id': int(line[0]),
                    'x': int(line[1]),
                    'y': int(line[2]),
                    'cooldown': int(line[3]),
                    'bombs': int(line[4]),
                    'wetness': int(line[5])
                }}
                agents.append(agent_data)

            my_agent_count = int(input())

            # Get my agents and enemy agents
            my_agents = [a for a in agents if a['id'] in my_agent_ids]
            enemy_agents = [a for a in agents if a['id'] not in my_agent_ids]

            # Output actions for each of my agents
            for agent in my_agents:
                if model is not None:
                    # Create observation for RL model
                    obs = create_observation(agent, enemy_agents, width, height)
                    action, _ = model.predict(obs, deterministic=True)
                    action_str = convert_action_to_command(action, agent, enemy_agents, width, height)
                else:
                    # Fallback to random actions
                    action_str = get_random_action(agent, width, height)

                print(f"{{agent['id']}};{{action_str}}")

            step_count += 1

        except EOFError:
            break
        except Exception as e:
            # Emergency fallback
            for agent_id in my_agent_ids:
                print(f"{{agent_id}};HUNKER_DOWN")
            break

def create_observation(my_agent, enemy_agents, width, height):
    """Create observation vector for the RL model"""
    obs = np.zeros(10, dtype=np.float32)

    # My agent state
    obs[0] = my_agent['x'] / width  # Normalized x
    obs[1] = my_agent['y'] / height  # Normalized y
    obs[2] = my_agent['wetness'] / 100.0  # Normalized wetness
    obs[3] = my_agent['cooldown'] / 10.0  # Normalized cooldown
    obs[4] = my_agent['bombs'] / 5.0  # Normalized bomb count

    # Enemy agent state (use first enemy or zeros)
    if enemy_agents:
        enemy = enemy_agents[0]
        obs[5] = enemy['x'] / width
        obs[6] = enemy['y'] / height
        obs[7] = enemy['wetness'] / 100.0

    # Map info
    obs[8] = width / 20.0  # Normalized width
    obs[9] = height / 20.0  # Normalized height

    return obs

def convert_action_to_command(action, my_agent, enemy_agents, width, height):
    """Convert RL action to game command"""
    if action == 0:
        return "HUNKER_DOWN"
    elif action == 1:
        # Move towards center
        center_x, center_y = width // 2, height // 2
        return f"MOVE {{center_x}} {{center_y}}"
    elif action == 2:
        # Shoot at medium range
        return "SHOOT 5"
    elif action == 3:
        # Throw bomb at enemy position
        if enemy_agents:
            enemy = enemy_agents[0]
            return f"THROW {{enemy['x']}} {{enemy['y']}}"
        else:
            return f"THROW {{width//2}} {{height//2}}"
    elif action == 4:
        # Move to random position
        x, y = random.randint(0, width-1), random.randint(0, height-1)
        return f"MOVE {{x}} {{y}}"
    else:
        # Shoot at max range
        return "SHOOT 10"

def get_random_action(agent, width, height):
    """Get a random action as fallback"""
    actions = [
        "HUNKER_DOWN",
        f"MOVE {{random.randint(0, width-1)}} {{random.randint(0, height-1)}}",
        f"SHOOT {{random.randint(1, 10)}}",
        f"THROW {{random.randint(0, width-1)}} {{random.randint(0, height-1)}}"
    ]
    return random.choice(actions)

if __name__ == "__main__":
    main()
'''

    # Write the bot script
    bot_filename = f"rl_bot_v{version}.py"
    bot_path = os.path.join("bots", bot_filename)

    with open(bot_path, 'w') as f:
        f.write(bot_script)

    # Make it executable
    os.chmod(bot_path, 0o755)

    return bot_path

def train_and_export_bot():
    """Train an RL model and export it as a bot for psyleague"""

    print("Initializing environment...")
    try:
        env = SoakOverflowEnv(opponent_bot_path="./bot")
        print("Environment created successfully!")
    except Exception as e:
        print(f"Error creating environment: {e}")
        print("Creating a simple bot without training...")
        # Create a bot that uses random actions
        bot_path = create_rl_bot_executable("dummy_model", "rl_bot", version=1)
        print(f"Random bot created at: {bot_path}")
        print("You can now add this bot to psyleague with:")
        print(f"psyleague bot add rl_bot_v1 -s {bot_path[:-3]}")
        return None, bot_path

    print("Creating PPO model...")
    try:
        model = PPO("MlpPolicy", env, verbose=1, learning_rate=0.0003)
        print("PPO model created successfully!")
    except Exception as e:
        print(f"Error creating PPO model: {e}")
        # Create a bot without training
        bot_path = create_rl_bot_executable("dummy_model", "rl_bot", version=1)
        return None, bot_path

    print("Training model...")
    try:
        # Start with a small number of timesteps for testing
        model.learn(total_timesteps=1000)
        print("Training completed!")
    except Exception as e:
        print(f"Error during training: {e}")
        print("Proceeding with untrained model...")

    print("Saving model...")
    model_path = "soak_agent"
    try:
        model.save(model_path)
        print(f"Model saved to {model_path}.zip")
    except Exception as e:
        print(f"Error saving model: {e}")
        model_path = "dummy_model"

    print("Creating executable bot...")
    bot_path = create_rl_bot_executable(model_path, "rl_bot", version=1)

    print(f"Bot created at: {bot_path}")
    print("You can now add this bot to psyleague with:")
    print(f"psyleague bot add rl_bot_v1 -s {bot_path[:-3]}")  # Remove .py extension

    # Test the bot by running a quick game
    print("\nTesting the bot...")
    try:
        result = subprocess.run([
            "python3", "play_game.py", bot_path, "./bot"
        ], capture_output=True, text=True, timeout=30)

        if result.returncode == 0:
            print("✅ Bot test successful!")
            print("Game result:", result.stdout.strip())
        else:
            print("⚠️ Bot test had issues:")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
    except Exception as e:
        print(f"⚠️ Bot test failed: {e}")

    return model, bot_path

if __name__ == "__main__":
    import subprocess
    train_and_export_bot()
