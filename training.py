from stable_baselines3 import PPO
from soak_env import SoakOverflowEnv
import os
import shutil

def create_rl_bot_executable(model_path, bot_name, version=0):
    """Create an executable bot that uses the trained RL model"""

    # Create the bot script content
    bot_script = f'''#!/usr/bin/env python3
import sys
import os
import numpy as np
from stable_baselines3 import PPO

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from soak_env import SoakOverflowEnv

def main():
    # Load the trained model
    model = PPO.load("{model_path}")

    # Initialize environment for inference
    env = SoakOverflowEnv()

    # Game loop
    obs = env.reset()

    while True:
        try:
            # Get action from trained model
            action, _states = model.predict(obs, deterministic=True)

            # Convert action to game format and output
            # This is a placeholder - you'll need to implement proper action conversion
            print("1;HUNKER_DOWN")  # Default action for now

            # Read next observation (this would come from game input)
            obs = env._read_obs()

        except EOFError:
            break
        except Exception as e:
            # Fallback to safe action
            print("1;HUNKER_DOWN")
            break

if __name__ == "__main__":
    main()
'''

    # Write the bot script
    bot_filename = f"rl_bot_v{version}.py"
    bot_path = os.path.join("bots", bot_filename)

    with open(bot_path, 'w') as f:
        f.write(bot_script)

    # Make it executable
    os.chmod(bot_path, 0o755)

    return bot_path

def train_and_export_bot():
    """Train an RL model and export it as a bot for psyleague"""

    print("Initializing environment...")
    env = SoakOverflowEnv()

    print("Creating PPO model...")
    model = PPO("MlpPolicy", env, verbose=1)

    print("Training model...")
    model.learn(total_timesteps=10_000)  # Reduced for faster testing

    print("Saving model...")
    model_path = "soak_agent"
    model.save(model_path)

    print("Creating executable bot...")
    bot_path = create_rl_bot_executable(model_path, "rl_bot", version=1)

    print(f"Bot created at: {bot_path}")
    print("You can now add this bot to psyleague with:")
    print(f"psyleague bot add rl_bot_v1 -s {bot_path[:-3]}")  # Remove .py extension

    return model, bot_path

if __name__ == "__main__":
    train_and_export_bot()
