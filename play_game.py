import sys, subprocess, random, json, tempfile, os
import re

def create_temp_main_java(bot_paths, seed):
    """Create a temporary Main.java file with the specified bots and seed"""
    java_code = f'''import com.codingame.gameengine.runner.MultiplayerGameRunner;

public class TempMain {{
    public static void main(String[] args) {{
        int LEAGUE = 5;

        MultiplayerGameRunner gameRunner = new MultiplayerGameRunner();
        gameRunner.setLeagueLevel(LEAGUE);
        gameRunner.setSeed({seed}L);

        // Add bots
        {chr(10).join([f'        gameRunner.addAgent("{bot_path}", "Bot{i+1}");' for i, bot_path in enumerate(bot_paths)])}

        gameRunner.start();
    }}
}}'''
    return java_code

def extract_game_results(output, seed):
    """Extract game results from the game engine output and format as JSON"""
    # Look for score information in the output
    lines = output.split('\n')

    # Default values
    ranks = [0, 1]  # Player 1 wins by default
    errors = [False, False]

    # Try to parse actual results from output
    for line in lines:
        # Look for score patterns or game end information
        if "score" in line.lower() or "winner" in line.lower():
            # This is where you'd parse actual game results
            # For now, we'll use a simple heuristic
            pass

    # Check for errors (timeouts, crashes, etc.)
    error_keywords = ["timeout", "error", "exception", "crash", "failed"]
    for line in lines:
        for keyword in error_keywords:
            if keyword.lower() in line.lower():
                if "bot1" in line.lower() or "player 0" in line.lower():
                    errors[0] = True
                elif "bot2" in line.lower() or "player 1" in line.lower():
                    errors[1] = True

    # If there are errors, adjust ranks accordingly
    if errors[0] and not errors[1]:
        ranks = [1, 0]  # Player 2 wins
    elif errors[1] and not errors[0]:
        ranks = [0, 1]  # Player 1 wins
    elif errors[0] and errors[1]:
        ranks = [0, 0]  # Draw due to both erroring

    return {
        "ranks": ranks,
        "errors": errors,
        "test_data": {"seed": seed},
        "player_data": [{}, {}]
    }

if __name__ == '__main__':
    if len(sys.argv) < 3:
        print(json.dumps({
            "ranks": [0, 1],
            "errors": [True, True],
            "test_data": {"error": "Not enough players"},
            "player_data": [{}, {}]
        }))
        sys.exit(1)

    # Generate random seed
    seed = random.randrange(0, 2**31)
    n_players = len(sys.argv) - 1
    bot_paths = sys.argv[1:n_players+1]

    # Create temporary Main.java file
    temp_java_file = os.path.join('GameEngine/src/test/java', 'TempMain.java')

    try:
        # Write temporary Java file
        with open(temp_java_file, 'w') as f:
            f.write(create_temp_main_java(bot_paths, seed))

        # Compile and run the game
        cmd = f'cd GameEngine && mvn test-compile exec:java -Dexec.mainClass="TempMain" -Dexec.classpathScope=test'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        # Extract and format results
        game_results = extract_game_results(result.stdout + result.stderr, seed)
        print(json.dumps(game_results))

    except Exception as e:
        # Error fallback
        print(json.dumps({
            "ranks": [0, 1],
            "errors": [True, True],
            "test_data": {"error": str(e), "seed": seed},
            "player_data": [{}, {}]
        }))
    finally:
        # Clean up temporary file
        if os.path.exists(temp_java_file):
            os.remove(temp_java_file)