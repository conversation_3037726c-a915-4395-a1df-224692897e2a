import sys, subprocess, random, json, tempfile, os

if __name__ == '__main__':
    # Create temporary log file
    f, log_file = tempfile.mkstemp(prefix='soak_log_')
    os.close(f)
    
    # Generate random seed
    seed = random.randrange(0, 2**31)
    n_players = len(sys.argv) - 1
    
    # Build command to run Java referee with bots
    cmd = f'cd GameEngine && mvn exec:java -Dexec.mainClass=com.codingame.game.Referee'
    for i in range(1, n_players+1):
        cmd += f' -Dexec.args="player{i}={sys.argv[i]}"'
    cmd += f' -Dseed={seed} -Dlog="{log_file}"'
    
    # Run the game
    task = subprocess.run(cmd, shell=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    
    # Parse results and output JSON
    # ... (parse game results from log file)