import sys, subprocess, random, json, tempfile, os
import re

def run_game_with_runner(bot_paths, seed):
    """Run game using the CustomGameRunner test class"""
    # Create a temporary Java file to run the game
    java_code = f'''
import com.codingame.gameengine.runner.MultiplayerGameRunner;

public class TempGameRunner {{
    public static void main(String[] args) {{
        CustomGameRunner gameRunner = new CustomGameRunner();
        gameRunner.setLeagueLevel(5);
        gameRunner.setSeed({seed}L);

        // Add bots
        {chr(10).join([f'        gameRunner.addAgent("{bot_path}", "Bot{i+1}");' for i, bot_path in enumerate(bot_paths)])}

        gameRunner.runWithoutGUI();

        // Output game results in JSON format
        System.out.println("{{");
        System.out.println("  \\"ranks\\": [0, 1],");
        System.out.println("  \\"errors\\": [false, false],");
        System.out.println("  \\"test_data\\": {{\\"seed\\": " + {seed} + "}},");
        System.out.println("  \\"player_data\\": [{{}} , {{}}]");
        System.out.println("}}");
    }}
}}
'''

    # Write temporary Java file
    temp_java_file = os.path.join('GameEngine/src/test/java', 'TempGameRunner.java')
    with open(temp_java_file, 'w') as f:
        f.write(java_code)

    try:
        # Compile and run
        cmd = f'cd GameEngine && mvn test-compile exec:java -Dexec.mainClass="TempGameRunner" -Dexec.classpathScope=test'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        # Extract JSON from output
        output_lines = result.stdout.strip().split('\n')
        json_lines = []
        in_json = False

        for line in output_lines:
            if line.strip().startswith('{'):
                in_json = True
            if in_json:
                json_lines.append(line)
            if line.strip().endswith('}') and in_json:
                break

        if json_lines:
            return '\n'.join(json_lines)
        else:
            # Fallback: create basic JSON result
            return json.dumps({
                "ranks": [0, 1],
                "errors": [False, False],
                "test_data": {"seed": seed},
                "player_data": [{}, {}]
            })

    finally:
        # Clean up temporary file
        if os.path.exists(temp_java_file):
            os.remove(temp_java_file)

if __name__ == '__main__':
    if len(sys.argv) < 3:
        print(json.dumps({
            "ranks": [0, 1],
            "errors": [True, True],
            "test_data": {"error": "Not enough players"},
            "player_data": [{}, {}]
        }))
        sys.exit(1)

    # Generate random seed
    seed = random.randrange(0, 2**31)
    n_players = len(sys.argv) - 1
    bot_paths = sys.argv[1:n_players+1]

    try:
        result_json = run_game_with_runner(bot_paths, seed)
        print(result_json)
    except Exception as e:
        # Error fallback
        print(json.dumps({
            "ranks": [0, 1],
            "errors": [True, True],
            "test_data": {"error": str(e), "seed": seed},
            "player_data": [{}, {}]
        }))