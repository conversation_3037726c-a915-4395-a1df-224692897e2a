# Psyleague Setup for RL Training

This guide explains how to use psyleague with your SoakOverflow game to train reinforcement learning models.

## Overview

The setup consists of:
- **GameEngine**: Java-based game engine for SoakOverflow
- **psyleague**: League system for automated bot matchmaking and evaluation
- **play_game.py**: Interface script between psyleague and GameEngine
- **training.py**: RL training script that exports bots for evaluation
- **bot.cpp**: Your existing random bot (baseline)

## Quick Start

### 1. Install Dependencies

```bash
# Install psyleague
pipx install psyleague

# Install Python dependencies for RL training
pip install stable-baselines3 gymnasium numpy
```

### 2. Add Your First Bots

```bash
# Add your existing random bot
psyleague bot add random_bot -s bot

# Add a second copy for testing
psyleague bot add random_bot2 -s bot
```

### 3. Start the League

```bash
# Run psyleague server (plays games automatically)
psyleague run --workers 1

# In another terminal, check the leaderboard
psyleague show
```

### 4. Train and Add RL Bots

```bash
# Train an RL model and export it as a bot
python3 training.py

# The script will create a bot in the bots/ directory
# Add it to psyleague (example):
# psyleague bot add rl_bot_v1 -s bots/rl_bot_v1
```

## How It Works

### Game Execution Flow

1. **psyleague** selects two bots for a match using TrueSkill matchmaking
2. **play_game.py** is called with the bot executables as arguments
3. **play_game.py** creates a temporary Java file and runs the GameEngine
4. Game results are returned as JSON to psyleague
5. **psyleague** updates TrueSkill ratings and schedules next games

### RL Training Integration

1. **Train model**: Use `training.py` to train a PPO agent
2. **Export bot**: The script creates an executable Python bot
3. **Add to league**: Use `psyleague bot add` to add the new bot
4. **Evaluate**: The bot automatically plays against other bots
5. **Iterate**: Analyze results and retrain with improvements

## Key Files

- `psyleague.cfg`: Configuration for psyleague (already set up)
- `play_game.py`: Interface between psyleague and GameEngine
- `training.py`: RL training script with bot export functionality
- `soak_env.py`: Gymnasium environment for RL training
- `bots/`: Directory where bot executables are stored

## Commands Reference

```bash
# Bot management
psyleague bot add <name> -s <source>     # Add a new bot
psyleague bot list                       # List all bots
psyleague bot remove <name>              # Remove a bot

# League operations
psyleague run --workers 1               # Start league server
psyleague show                          # Show leaderboard
psyleague show --output csv             # Export as CSV
psyleague games                         # Show recent games

# Advanced
psyleague recalc                        # Recalculate ratings
psyleague reset                         # Reset all data
```

## Training Workflow

1. **Baseline**: Start with your random bot as baseline
2. **Train**: Use `training.py` to train initial RL model
3. **Evaluate**: Add trained bot to psyleague and let it play
4. **Analyze**: Check performance vs baseline bots
5. **Iterate**: Improve training based on results
6. **Scale**: Add multiple bot versions to track progress

## Benefits for RL Training

- **Diverse opponents**: Your RL agent faces various strategies
- **Automatic evaluation**: No manual game orchestration needed
- **Progress tracking**: TrueSkill ratings show improvement over time
- **Statistical significance**: Hundreds of games played automatically
- **Continuous testing**: Add new bot versions without stopping

## Next Steps

1. Complete the `soak_env.py` implementation for proper RL training
2. Improve the `play_game.py` result parsing for more accurate game outcomes
3. Create more sophisticated baseline bots (heuristic strategies)
4. Set up automated training pipelines that periodically add new bot versions

The system is now ready for RL training! Start with `psyleague run` and begin training your first RL agent.
