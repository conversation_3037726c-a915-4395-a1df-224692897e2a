<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.codingame.game</groupId>
	<artifactId>summer-challenge-2025-super-soaker</artifactId>
	<version>1.0-SNAPSHOT</version>

    <properties>
        <gamengine.version>4.5.0</gamengine.version>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.codingame.gameengine</groupId>
            <artifactId>core</artifactId>
            <version>${gamengine.version}</version>
        </dependency>
        
        <dependency>
            <groupId>com.codingame.gameengine</groupId>
            <artifactId>module-endscreen</artifactId>
            <version>${gamengine.version}</version>
        </dependency>

        <dependency>
            <groupId>com.codingame.gameengine</groupId>
            <artifactId>runner</artifactId>
            <version>${gamengine.version}</version>
        </dependency>
    </dependencies>
</project>
