import com.codingame.gameengine.runner.MultiplayerGameRunner;

public class Main {
    public static void main(String[] args) {

        int LEAGUE = 5;

        MultiplayerGameRunner gameRunner = new MultiplayerGameRunner();
        gameRunner.setLeagueLevel(LEAGUE);

        // Set seed here (leave commented for random)
        // gameRunner.setSeed(-1566415677164768800L);

        // Select agents here

        gameRunner.addAgent("/home/<USER>/progra/codingame/bot", "RAZOG");
        gameRunner.addAgent("/home/<USER>/progra/codingame/bot", "RAZOG2");

        gameRunner.start();
    }
}
