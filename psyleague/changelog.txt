[current]
* show: support for openskill & global (order-invariant) rating systems
* db recreate: new command for regenerating .db, useful if something was corrupted or after merging multiple .games files, optionally you can shuffle all games with --shuffle
* db verify: new command for checking if .db is correct
* show/info: now supports different output formats via --output FORMAT, where FORMAT is table, json or csv
* show/info: add color support for odd/even rows in --output table (configurable via cfg)
* show: --include/--exclude recalculates ranking after include/excluding all bots matching specified regexes
* bot add: when you submit a bot, it will run a single game against itself to check for crashes before adding it to db (configurable via cfg)
* bot add: --setup to override cmd_bot_setup from cfg
* run: only a single run is allowed, throws error when you want to run multiple run in parallel
* progress bar when performing ranking recalculation (you can turn it off via cfg)
* rating calculation can skip errors now (configurable via cfg), useful if you want to reduce impact of random timeouts
* [fix] pressing ctrl+c while saving db should no longer corrupt .db file (hopefully!)
* [fix] any ranking recalculation (usually removing a bot) would reset active status of all remaining bots 
[0.4.1]
* [fix] info, bot remove, bot update should correctly work games with 3+ players
[0.4.0]
* support for games with 3+ players (requires updating n_players & cmd_play_game in the cfg file); example play_game script is updated as well
* show: --recent is sorted by score & shows position in the overall ranking as well
* [fix] PDATA_ALL is always sorted
[0.3.1]
* info: new command to show detailed stats about specific bot (for now it's just winrates)
* [fix] PDATA_ALL now works correctly
[0.3.0]
* games can now store additional custom data (per test or per player):
    - you can use test data to recalculate ratings on a subset of games (via show --filters)
    - you can display player data on the leaderboard (check out the config file for more information)
    - play_game script now uses a new JSON format and allows for returning multiple games; old format has been removed
    - .games file: format changed to JSON
    - see readme.md for CodinGame-specific play_game script example that uses the new standard and allows for easy data extraction
* run: -w/--workers N overrides the number of workers set in the config file
* show: -s/--resample N displays the ranking using bootstrapped (resampling with replacement) with N games
* bot remove: removes a bot along with all games and recalculates the entire ranking (execution might take a while)
* bot update: -n/--new_name renames bot (this is only partially supported as this doesn't change the bot executable file; warning: can be slow as it updates the whole .games file)
* bot stop/remove: you can stop/remove multiple bots by specifying a comma-separated list of names
* some bugfixes and better error checking in a few places
[0.2.0]
* add support for retrieving error data (requires update of play_game script!)
* run: can run a fixed number of games with --games
* show: support for custom leaderboard, new leaderboard config option defines columns to show
* show: you can see bot's creation date, change the format with date_format config option
* show: --limit -> --best
* show: added --recent to show the most recent X created bots
* [fix] psyleague show --active had no effect
* [fix] logs show your local timezone now
[0.1.1]
* [fixes] psyleague config, psyleague bot update -d, installation, linux support
[0.1.0] 
* initial version