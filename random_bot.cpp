#include <iostream>
#include <string>
#include <vector>
#include <random>
#include <ctime>

using namespace std;

int main() {
    // Initialize random number generator
    srand(time(nullptr));
    
    int my_id;
    cin >> my_id; cin.ignore();
    
    int agent_data_count;
    cin >> agent_data_count; cin.ignore();
    
    vector<int> my_agent_ids;
    
    // Read agent data
    for (int i = 0; i < agent_data_count; i++) {
        int agent_id, player_id, shoot_cooldown, optimal_range, soaking_power, max_splash_bombs;
        cin >> agent_id >> player_id >> shoot_cooldown >> optimal_range >> soaking_power >> max_splash_bombs;
        cin.ignore();
        
        if (player_id == my_id) {
            my_agent_ids.push_back(agent_id);
        }
    }
    
    int width, height;
    cin >> width >> height; cin.ignore();
    
    // Read map data
    for (int i = 0; i < width * height; i++) {
        int x, y, type;
        cin >> x >> y >> type; cin.ignore();
    }
    
    // Game loop
    while (true) {
        int agent_count;
        cin >> agent_count; cin.ignore();
        
        // Read agent states
        for (int i = 0; i < agent_count; i++) {
            int agent_id, x, y, cooldown, splash_bombs, wetness;
            cin >> agent_id >> x >> y >> cooldown >> splash_bombs >> wetness;
            cin.ignore();
        }
        
        int my_agent_count;
        cin >> my_agent_count; cin.ignore();
        
        // Output random actions for each of my agents
        for (int agent_id : my_agent_ids) {
            vector<string> actions = {
                "HUNKER_DOWN",
                "MOVE " + to_string(rand() % width) + " " + to_string(rand() % height),
                "SHOOT " + to_string(rand() % 10 + 1),
                "THROW " + to_string(rand() % width) + " " + to_string(rand() % height)
            };
            
            string chosen_action = actions[rand() % actions.size()];
            cout << agent_id << ";" << chosen_action << endl;
        }
    }
    
    return 0;
}
