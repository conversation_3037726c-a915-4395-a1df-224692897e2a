// #undef _GLIBCXX_DEBUG                // disable run-time bound checking, etc
// #pragma GCC optimize("Ofast,inline") // Ofast = O3,fast-math,allow-store-data-races,no-protect-parens

// #ifndef __POPCNT__ // not march=generic
// #endif

// #pragma GCC target("bmi,bmi2,lzcnt,popcnt")                      // bit manipulation
// #pragma GCC target("movbe")                                      // byte swap
// #pragma GCC target("aes,pclmul,rdrnd")                           // encryption
// #pragma GCC target("avx,avx2,f16c,fma,sse3,ssse3,sse4.1,sse4.2") // SIMD
// #pragma GCC optimize "Ofast,unroll-loops,omit-frame-pointer,inline"
// // #pragma GCC option("arch=native", "tune=native", "no-zero-upper")
// #pragma GCC target("rdrnd", "popcnt", "avx", "bmi2")

#include <iostream>
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <optional>
#include <cmath>
#include <random>
#include <array>



using namespace std;
random_device rd;
mt19937 rng(rd());

// Represents initial agent configuration
struct AgentClass {
	int agent_id;
	int player_id;
	int shoot_cooldown;
	int optimal_range;
	int soaking_power;
	int max_splash_bombs;
};

// Represents current state of an agent
struct AgentState {
	int agent_id;
	int x, y;
	int cooldown;
	int splash_bombs;
	int wetness;
	bool hunkered;
};

// Represents a tile on the map
struct Tile {
	int type = 0; // 0 = floor, 1 = low cover, 2 = high cover
};

// Fast hash for coordinate key (used in unordered_map)
struct Coord {
	int x, y;
	Coord(int x = 0, int y = 0) : x(x), y(y) {}
	bool operator==(const Coord& other) const { return x == other.x && y == other.y; }
};

struct CoordHash {
	size_t operator()(const Coord& c) const {
		return hash<int>()(c.x) ^ (hash<int>()(c.y) << 1);
	}
};

enum class ActionType { MOVE, SHOOT, THROW, HUNKER_DOWN, MESSAGE };
struct Action {
	ActionType  type;
	Coord       target;
	int         target_id;
	string      message;

	Action(ActionType t) : type(t), target_id(-1) {}
	Action(ActionType t, Coord pos) : type(t), target(pos), target_id(-1) {}
	Action(ActionType t, int id) : type(t), target_id(id) {}
	Action(ActionType t, string msg) : type(t), target_id(-1), message(std::move(msg)) {}

	string toString() const {
		switch (type) {
			case ActionType::MOVE: return "MOVE " + to_string(target.x) + " " + to_string(target.y);
			case ActionType::SHOOT: return "SHOOT " + to_string(target_id);
			case ActionType::THROW: return "THROW " + to_string(target.x) + " " + to_string(target.y);
			case ActionType::HUNKER_DOWN: return "HUNKER_DOWN";
			case ActionType::MESSAGE: return "MESSAGE " + message;
		}
		return "";
	}
};

struct CompoundAction {
	optional<Action> move;
	optional<Action> combat;

	string toString() const {
		if (move && combat) return move->toString() + ";" + combat->toString();
		else if (move) return move->toString();
		else return combat->toString();
	}
};


// GameState class with optimized containers
class GameState {
public:
	int my_id;
	int width, height;

	unordered_map<int, AgentClass> agent_classes;       // agent_id -> class info
	unordered_map<int, AgentState> current_agents;      // agent_id -> current state
	unordered_map<Coord, Tile, CoordHash> map_tiles;    // (x,y) -> tile info
	vector<int> my_agent_ids;                           // your agent IDs
	unordered_set<Coord, CoordHash> empty_tiles;     // (x,y) -> Empty tiles

	void parseInitialInput() {
		cin >> my_id; cin.ignore();

		int agent_data_count;
		cin >> agent_data_count; cin.ignore();

		agent_classes.reserve(agent_data_count);
		my_agent_ids.reserve(agent_data_count);

		for (int i = 0; i < agent_data_count; ++i) {
			AgentClass ac;
			cin >> ac.agent_id >> ac.player_id >> ac.shoot_cooldown >> ac.optimal_range
				>> ac.soaking_power >> ac.max_splash_bombs;
			cin.ignore();

			agent_classes[ac.agent_id] = ac;
			if (ac.player_id == my_id)
				my_agent_ids.push_back(ac.agent_id);
		}

		cin >> width >> height; cin.ignore();
		for (int i = 0; i < width * height; ++i) {
			int x, y, type;
			cin >> x >> y >> type; cin.ignore();
			map_tiles[{x, y}] = Tile{type};
		}
	}

	void	updateEmptyTiles() {
		empty_tiles.clear();
		empty_tiles.reserve(width * height);

		// Pre-mark all walkable tiles
		for (const auto& [coord, tile] : map_tiles) {
			if (!tile.type) { // Walkable
				empty_tiles.insert(coord);
			}
		}

		// Remove tiles occupied by alive agents
		for (const auto& [_, agent] : current_agents) {
			if (agent.wetness < 100) {
				Coord pos{agent.x, agent.y};
				empty_tiles.erase(pos);
			}
		}
	}

	void parseTurnInput() {
		current_agents.clear();

		int agent_count;
		cin >> agent_count; cin.ignore();
		current_agents.reserve(agent_count);

		for (int i = 0; i < agent_count; ++i) {
			AgentState ag;
			cin >> ag.agent_id >> ag.x >> ag.y >> ag.cooldown >> ag.splash_bombs >> ag.wetness;
			cin.ignore();
			ag.hunkered = false;
			current_agents[ag.agent_id] = ag;
		}
		int my_agent_count;
		cin >> my_agent_count;
		cin.ignore();

		updateEmptyTiles();
	}

	vector<AgentState> getMyAgents() const {
		vector<AgentState> result;
		result.reserve(my_agent_ids.size());
		for (int id : my_agent_ids) {
			auto it = current_agents.find(id);
			if (it != current_agents.end())
				result.push_back(it->second);
		}
		return result;
	}

	Tile getTile(int x, int y) const {
		Coord c{x, y};
		auto it = map_tiles.find(c);
		if (it != map_tiles.end()) return it->second;
		return Tile{0}; // Default to floor
	}

	bool isInsideMap(const Coord& pos) const {
		return pos.x >= 0 && pos.x < width && pos.y >= 0 && pos.y < height;
	}

	vector<Action>  generateMoveActions(const AgentState& agent) const
	{
		cerr << agent.agent_id << ": agent_id\n";
		vector<Action>	moves;
		static const array<Coord, 4> directions = {
			Coord(0, 1), Coord(0, -1), Coord(1, 0), Coord(-1, 0)
		};

		for (const Coord& dir : directions)
		{
			Coord target{ agent.x + dir.x, agent.y + dir.y };
			
			if (empty_tiles.count(target))
				moves.push_back(Action{ ActionType::MOVE, target });
		}

		return moves;
	}

	vector<Action>	generateShootActions(const AgentState& agent) const
	{
		vector<Action> actions;

		if (agent.cooldown > 0 || agent.wetness >= 100)
			return actions;

		auto it = agent_classes.find(agent.agent_id);
		if (it == agent_classes.end())
			return actions;

		const AgentClass& ac = it->second;
		int	max_range = ac.optimal_range * 2;

		for (const auto& [id, target] : current_agents)
		{
			if (target.agent_id == agent.agent_id || agent_classes.at(target.agent_id).player_id == my_id)
				continue;

			if (target.wetness >= 100)
				continue;

			int distance = abs(agent.x - target.x) + abs(agent.y - target.y);
			if (distance < max_range)
				actions.push_back(Action{ ActionType::SHOOT, target.agent_id });
		}

		return actions;
	}

	vector<Action> generateThrowActions(const AgentState& agent) const {
		vector<Action> actions;

		if (agent.splash_bombs <= 0 || agent.wetness >= 100)
			return actions;

		constexpr int MAX_THROW_RANGE = 4;

		for (int dx = -MAX_THROW_RANGE; dx <= MAX_THROW_RANGE; ++dx) {
			for (int dy = -MAX_THROW_RANGE; dy <= MAX_THROW_RANGE; ++dy) {
				Coord target(agent.x + dx, agent.y + dy);
				if (!isInsideMap(target)) continue;

				int distance = abs(dx) + abs(dy);
				if (distance > MAX_THROW_RANGE) continue;

				// Check 3x3 AoE for at least one enemy

				for (int ox = -1; ox <= 1; ++ox) {
					for (int oy = -1; oy <= 1; ++oy) {
						Coord affected(target.x + ox, target.y + oy);
						if (!isInsideMap(affected)) continue;

						for (const auto& [id, other] : current_agents) {
							if (other.wetness >= 100) continue;
							if (other.x != affected.x || other.y != affected.y) continue;

							int owner = agent_classes.at(other.agent_id).player_id;
							if (owner != my_id) {
								actions.push_back(Action(ActionType::THROW, target));
								goto next_throw_target;
							}
						}
					}
				}

			next_throw_target:
				continue;
			}
		}

		return actions;
	}

	vector<Action> generateCombatActions(const AgentState& agent) const {
		vector<Action> actions;

		// Generate valid SHOOT actions
		vector<Action> shoot_actions = generateShootActions(agent);
		actions.insert(actions.end(), shoot_actions.begin(), shoot_actions.end());

		// Generate valid THROW actions
		vector<Action> throw_actions = generateThrowActions(agent);
		actions.insert(actions.end(), throw_actions.begin(), throw_actions.end());

		// Always include HUNKER_DOWN
		actions.push_back(Action(ActionType::HUNKER_DOWN));

		return actions;
	}

	vector<CompoundAction> generateCompoundActions(const AgentState& agent) const {
		vector<CompoundAction> combos;

		// First, try NO MOVE (stay in place)
		AgentState standing = agent;
		auto combat_options = generateCombatActions(standing);
		for (const Action& c : combat_options)
			combos.push_back({nullopt, c});

		// Then try all valid moves
		auto move_actions = generateMoveActions(agent);
		for (const Action& move : move_actions) {
			combos.push_back({move, nullopt}); // only move action
			AgentState moved = agent;
			moved.x = move.target.x;
			moved.y = move.target.y;

			auto combat_after_move = generateCombatActions(moved);
			for (const Action& c : combat_after_move)
				combos.push_back({move, c});
		}

		return combos;
	}

	double getRangeModifier(const AgentState& target, const AgentState& shooter) const {
		int distanceToTarget = abs(target.x - shooter.x) + abs(target.y - shooter.y);
		int opt = agent_classes.at(shooter.agent_id).optimal_range;
		if (distanceToTarget <= opt) {
			return 1.0;
		}
		if (distanceToTarget <= opt * 2) {
			return 0.5;
		}
		return 0.0;
	}

	double getCoverModifier(const AgentState& target, const AgentState& shooter) const {
		double dx = target.x - shooter.x;
		double dy = target.y - shooter.y;
		int bestModifier = 1;

		// check along X and Y axes exactly as in Java
		for (auto d : { array<double,2>{dx, 0.0}, array<double,2>{0.0, dy} }) {
			if (abs(d[0]) > 1.0 || abs(d[1]) > 1.0) {
				int adjX = (int)-copysign(1.0, d[0]);
				int adjY = (int)-copysign(1.0, d[1]);
				Coord coverPos { target.x + adjX, target.y + adjY };

				// chebyshev distance > 1
				int cheb = max(abs(coverPos.x - shooter.x),
									abs(coverPos.y - shooter.y));
				if (isInsideMap(coverPos) && cheb > 1) {
					bestModifier = min(bestModifier,
						getTile(coverPos.x, coverPos.y).type
					);
				}
			}
		}
		return bestModifier;
	}

	double getHunkeredModifierBonus(const AgentState& target) const {
		return .25;
	}

	int calculateShotDamage(const AgentState& target, const AgentState& shooter) const {
		double rangeModifier  = getRangeModifier(target, shooter);
		double coverModifier  = getCoverModifier(target, shooter);
		double hunkerBonus    = getHunkeredModifierBonus(target);
		int soakingPower      = agent_classes.at(shooter.agent_id).soaking_power;

		double raw = soakingPower * rangeModifier * (coverModifier - hunkerBonus);
		return static_cast<int>(round(raw));
	}

	void debugPrint() const {
		cerr << "=== DEBUG INFO ===" << endl;
		cerr << "Player ID: " << my_id << endl;
		cerr << "Map Size: " << width << "x" << height << " (tiles stored: " << map_tiles.size() << ")" << endl;

		cerr << "\nAgent Classes:\n";
		for (const auto& [id, ac] : agent_classes) {
			cerr << "  Agent " << id << " (Player " << ac.player_id << "): CD=" << ac.shoot_cooldown
				 << ", Range=" << ac.optimal_range << ", Power=" << ac.soaking_power
				 << ", Bombs=" << ac.max_splash_bombs << endl;
		}

		cerr << "\nCurrent Agents:\n";
		for (const auto& [id, ag] : current_agents) {
			cerr << "  Agent " << id << ": Pos=(" << ag.x << "," << ag.y << "), CD=" << ag.cooldown
				 << ", Bombs=" << ag.splash_bombs << ", Wetness=" << ag.wetness << endl;
		}

		cerr << "\nMy Agents: ";
		for (int id : my_agent_ids) cerr << id << " ";
		cerr << endl << "==================" << endl;
	}
};


int main(int ac, char **av) {
	bool rl_mode = (ac > 1 && string(av[1]) == "rl"); 
	
	GameState game;
	game.parseInitialInput();
	while (true) {
		game.parseTurnInput();

		if (rl_mode) {
			string line;
			getline(cin, line);
			cout << line << endl;
			continue;
		}

		// game.debugPrint();
		for (int id : game.my_agent_ids)
		{
			if (game.current_agents.find(id) == game.current_agents.end())
				continue;
			vector<CompoundAction> combos = game.generateCompoundActions(game.current_agents.at(id));
			if (!combos.empty())
			{
				uniform_int_distribution<int> dist(0, combos.size() - 1);
				const CompoundAction& randomAction = combos[dist(rng)];
				cout << id << ';' << randomAction.toString() << endl;
			}
			else
				cout << id << ";HUNKER_DOWN;" << endl;
		}
	}
	return 0;
}
