#!/usr/bin/env python3
import sys
import os
import numpy as np
import random

# Try to import stable_baselines3, fallback to random if not available
try:
    from stable_baselines3 import PPO
    MODEL_AVAILABLE = True
except ImportError:
    MODEL_AVAILABLE = False
    print("Warning: stable_baselines3 not available, using random actions", file=sys.stderr)

def main():
    # Read initial game setup
    my_id = int(input())
    agent_data_count = int(input())

    my_agent_ids = []
    for _ in range(agent_data_count):
        line = input().split()
        agent_id, player_id = int(line[0]), int(line[1])
        if player_id == my_id:
            my_agent_ids.append(agent_id)

    width, height = map(int, input().split())

    # Read map data
    for _ in range(width * height):
        input()  # x, y, type

    # Load model if available
    model = None
    if MODEL_AVAILABLE and os.path.exists("soak_agent.zip"):
        try:
            model = PPO.load("soak_agent")
        except Exception as e:
            print(f"Error loading model: {e}", file=sys.stderr)
            model = None

    # Game loop
    step_count = 0
    while True:
        try:
            agent_count = int(input())

            # Read agent states
            agents = []
            for _ in range(agent_count):
                line = input().split()
                agent_data = {
                    'id': int(line[0]),
                    'x': int(line[1]),
                    'y': int(line[2]),
                    'cooldown': int(line[3]),
                    'bombs': int(line[4]),
                    'wetness': int(line[5])
                }
                agents.append(agent_data)

            my_agent_count = int(input())

            # Get my agents and enemy agents
            my_agents = [a for a in agents if a['id'] in my_agent_ids]
            enemy_agents = [a for a in agents if a['id'] not in my_agent_ids]

            # Output actions for each of my agents
            for agent in my_agents:
                if model is not None:
                    # Create observation for RL model
                    obs = create_observation(agent, enemy_agents, width, height)
                    action, _ = model.predict(obs, deterministic=True)
                    action_str = convert_action_to_command(action, agent, enemy_agents, width, height)
                else:
                    # Fallback to random actions
                    action_str = get_random_action(agent, width, height)

                print(f"{agent['id']};{action_str}")

            step_count += 1

        except EOFError:
            break
        except Exception as e:
            # Emergency fallback
            for agent_id in my_agent_ids:
                print(f"{agent_id};HUNKER_DOWN")
            break

def create_observation(my_agent, enemy_agents, width, height):
    """Create observation vector for the RL model"""
    obs = np.zeros(10, dtype=np.float32)

    # My agent state
    obs[0] = my_agent['x'] / width  # Normalized x
    obs[1] = my_agent['y'] / height  # Normalized y
    obs[2] = my_agent['wetness'] / 100.0  # Normalized wetness
    obs[3] = my_agent['cooldown'] / 10.0  # Normalized cooldown
    obs[4] = my_agent['bombs'] / 5.0  # Normalized bomb count

    # Enemy agent state (use first enemy or zeros)
    if enemy_agents:
        enemy = enemy_agents[0]
        obs[5] = enemy['x'] / width
        obs[6] = enemy['y'] / height
        obs[7] = enemy['wetness'] / 100.0

    # Map info
    obs[8] = width / 20.0  # Normalized width
    obs[9] = height / 20.0  # Normalized height

    return obs

def convert_action_to_command(action, my_agent, enemy_agents, width, height):
    """Convert RL action to game command"""
    if action == 0:
        return "HUNKER_DOWN"
    elif action == 1:
        # Move towards center
        center_x, center_y = width // 2, height // 2
        return f"MOVE {center_x} {center_y}"
    elif action == 2:
        # Shoot at medium range
        return "SHOOT 5"
    elif action == 3:
        # Throw bomb at enemy position
        if enemy_agents:
            enemy = enemy_agents[0]
            return f"THROW {enemy['x']} {enemy['y']}"
        else:
            return f"THROW {width//2} {height//2}"
    elif action == 4:
        # Move to random position
        x, y = random.randint(0, width-1), random.randint(0, height-1)
        return f"MOVE {x} {y}"
    else:
        # Shoot at max range
        return "SHOOT 10"

def get_random_action(agent, width, height):
    """Get a random action as fallback"""
    actions = [
        "HUNKER_DOWN",
        f"MOVE {random.randint(0, width-1)} {random.randint(0, height-1)}",
        f"SHOOT {random.randint(1, 10)}",
        f"THROW {random.randint(0, width-1)} {random.randint(0, height-1)}"
    ]
    return random.choice(actions)

if __name__ == "__main__":
    main()
