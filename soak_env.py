import gymnasium as gym
import subprocess
import numpy as np
import tempfile
import os
import json
import random
from typing import Dict, List, Tuple, Any

class SoakOverflowEnv(gym.Env):
    """
    Gymnasium environment for SoakOverflow game.
    This environment runs games against the random bot and provides observations/rewards.
    """

    def __init__(self, opponent_bot_path="./bot"):
        super().__init__()

        self.opponent_bot_path = opponent_bot_path
        self.game_proc = None
        self.current_step = 0
        self.max_steps = 200  # Maximum game length

        # Game state
        self.my_id = 0  # We are always player 0
        self.my_agents = []
        self.enemy_agents = []
        self.map_width = 0
        self.map_height = 0
        self.game_over = False
        self.last_reward = 0.0

        # Define action and observation spaces
        # Action space: for each agent, we can choose from several action types
        # For simplicity, we'll use a discrete action space with predefined actions
        self.action_space = gym.spaces.Discrete(6)  # HUNKER_DOWN, MOVE_RANDOM, SHOOT_NEAREST, etc.

        # Observation space: simplified state representation
        # [my_agent_x, my_agent_y, my_agent_wetness, my_agent_cooldown, my_agent_bombs,
        #  enemy_agent_x, enemy_agent_y, enemy_agent_wetness, map_width, map_height]
        self.observation_space = gym.spaces.Box(
            low=0, high=100, shape=(10,), dtype=np.float32
        )

    def reset(self, seed=None, options=None):
        """Reset the environment and start a new game."""
        super().reset(seed=seed)

        if self.game_proc:
            self.game_proc.terminate()
            self.game_proc = None

        self.current_step = 0
        self.game_over = False
        self.last_reward = 0.0

        # Start a new game by running play_game.py with our RL bot vs opponent
        # We'll create a temporary RL bot that reads from stdin
        self.temp_bot_path = self._create_temp_rl_bot()

        try:
            # Run the game
            result = subprocess.run([
                "python3", "play_game.py", self.temp_bot_path, self.opponent_bot_path
            ], capture_output=True, text=True, timeout=30)

            # Parse the initial game state from the result
            # For now, return a simple observation
            obs = self._get_initial_observation()
            info = {"game_started": True}

            return obs, info

        except Exception as e:
            print(f"Error starting game: {e}")
            return self._get_default_observation(), {"error": str(e)}

    def step(self, action):
        """Execute an action and return the new state."""
        self.current_step += 1

        # Convert action to game command
        action_str = self._action_to_command(action)

        # For this simplified version, we'll simulate the game step
        # In a full implementation, you'd send the action to the game process

        # Get new observation
        obs = self._get_observation_after_action(action)

        # Calculate reward
        reward = self._calculate_reward(action)

        # Check if game is done
        terminated = self.current_step >= self.max_steps or self.game_over
        truncated = False

        info = {
            "step": self.current_step,
            "action_taken": action_str
        }

        return obs, reward, terminated, truncated, info

    def _create_temp_rl_bot(self):
        """Create a temporary bot executable that will be controlled by the RL agent."""
        bot_script = '''#!/usr/bin/env python3
import sys
import random

def main():
    # Read initial game setup
    my_id = int(input())
    agent_data_count = int(input())

    my_agent_ids = []
    for _ in range(agent_data_count):
        line = input().split()
        agent_id, player_id = int(line[0]), int(line[1])
        if player_id == my_id:
            my_agent_ids.append(agent_id)

    width, height = map(int, input().split())

    # Read map data
    for _ in range(width * height):
        input()  # x, y, type

    # Game loop
    while True:
        try:
            agent_count = int(input())

            # Read agent states
            for _ in range(agent_count):
                input()  # agent_id, x, y, cooldown, splash_bombs, wetness

            my_agent_count = int(input())

            # Output actions for each agent
            for agent_id in my_agent_ids:
                # For now, just output random valid actions
                actions = [
                    "HUNKER_DOWN",
                    f"MOVE {random.randint(0, width-1)} {random.randint(0, height-1)}",
                    f"SHOOT {random.randint(1, 10)}",
                    f"THROW {random.randint(0, width-1)} {random.randint(0, height-1)}"
                ]
                chosen_action = random.choice(actions)
                print(f"{agent_id};{chosen_action}")

        except EOFError:
            break

if __name__ == "__main__":
    main()
'''

        # Create temporary file
        fd, temp_path = tempfile.mkstemp(suffix='.py', prefix='rl_bot_')
        with os.fdopen(fd, 'w') as f:
            f.write(bot_script)

        # Make it executable
        os.chmod(temp_path, 0o755)
        return temp_path

    def _action_to_command(self, action):
        """Convert discrete action to game command string."""
        action_map = {
            0: "HUNKER_DOWN",
            1: "MOVE 5 5",  # Move to center
            2: "SHOOT 5",   # Shoot with medium range
            3: "THROW 5 5", # Throw bomb to center
            4: "MOVE 0 0",  # Move to corner
            5: "SHOOT 10"   # Shoot with max range
        }
        return action_map.get(action, "HUNKER_DOWN")

    def _get_initial_observation(self):
        """Get initial observation after game start."""
        # Simplified observation - in practice you'd parse actual game state
        return np.array([
            5.0, 5.0, 0.0, 0.0, 3.0,  # My agent: x, y, wetness, cooldown, bombs
            10.0, 10.0, 0.0, 15.0, 15.0  # Enemy agent: x, y, wetness, map_width, map_height
        ], dtype=np.float32)

    def _get_observation_after_action(self, action):
        """Get observation after taking an action."""
        # Simulate some state changes based on action
        obs = self._get_initial_observation()

        # Add some noise/changes based on action
        if action == 1:  # MOVE
            obs[0] += random.uniform(-1, 1)  # x position change
            obs[1] += random.uniform(-1, 1)  # y position change
        elif action == 2:  # SHOOT
            obs[3] = 3.0  # Set cooldown

        # Simulate enemy movement
        obs[5] += random.uniform(-1, 1)
        obs[6] += random.uniform(-1, 1)

        return obs

    def _get_default_observation(self):
        """Get default observation when there's an error."""
        return np.zeros(10, dtype=np.float32)

    def _calculate_reward(self, action):
        """Calculate reward based on action and game state."""
        # Simple reward function - you can make this more sophisticated
        reward = 0.0

        # Small negative reward for each step (encourages finishing quickly)
        reward -= 0.01

        # Reward for taking aggressive actions
        if action in [2, 3]:  # SHOOT or THROW
            reward += 0.1

        # Penalty for just hunkering down
        if action == 0:
            reward -= 0.05

        # Random component to simulate game outcomes
        if random.random() < 0.1:  # 10% chance of getting hit
            reward -= 1.0

        if random.random() < 0.05:  # 5% chance of hitting enemy
            reward += 2.0

        return reward

    def close(self):
        """Clean up resources."""
        if self.game_proc:
            self.game_proc.terminate()
            self.game_proc = None

        # Clean up temporary bot file
        if hasattr(self, 'temp_bot_path') and os.path.exists(self.temp_bot_path):
            os.unlink(self.temp_bot_path)
