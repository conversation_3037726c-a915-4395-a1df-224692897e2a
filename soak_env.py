import gym
import subprocess
import numpy as np

class SoakOverflowEnv(gym.Env):
    def __init__(self):
        self.proc = None
        self.agent_ids = [0, 1]  # update if needed

    def reset(self):
        if self.proc: self.proc.kill()
        self.proc = subprocess.Popen(
            ["mvn", "exec:java", "-Dexec.mainClass=Main", "-Dexec.classpathScope=test"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            text=True
        )
        # TODO: Wait for initial state lines and return obs
        return self._read_obs()

    def step(self, actions):
        # actions = ["0;MOVE 3 3;SHOOT 5", "1;HUNKER_DOWN"]
        for a in actions:
            self.proc.stdin.write(a + "\n")
        self.proc.stdin.flush()

        # TODO: Read new state and reward
        obs = self._read_obs()
        reward = self._compute_reward()
        done = self._check_done()
        return obs, reward, done, {}

    def _read_obs(self):
        # TODO: Parse stdout from bot or engine
        return np.zeros((1,))

    def _compute_reward(self):
        # TODO: Parse output or logs to determine reward
        return 0.0

    def _check_done(self):
        # TODO: Detect end of match from logs
        return False

    def close(self):
        if self.proc:
            self.proc.kill()
