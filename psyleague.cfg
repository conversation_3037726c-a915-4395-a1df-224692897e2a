version = "0.4.1"

# [GENERAL]
n_workers = 1 # number of games played in parallel; note that the actual number of used cores might be higher depending on how the games are implemented
n_players = 2 # number of players in a game, you only need to change that if your game is not 1v1
selfplay_check = false # if true, when you add a bot, it's going to play a game against itself to check if it's working properly
skip_errors = false # if true, the games that end up with errors are going to be skipped when calculating the ranking (useful if you're worried that random time outs might affect the rankings)

# [MATCHMAKING]
mm_min_matches = 50 # Reduced for faster initial evaluation
mm_min_matches_preference = 1.0

# [SHOW]
show_output = "table" # default format of the output, allowed values: table, csv, json; you can override this via --output

show_progress = true # is progress bar visible during ranking recalculation

show_colors = true 
# allowed colors: BLACK, RED, GREEN, YELLOW, BLUE, MAGENTA, CYAN, WHITE, DEFAULT (DEFAULT means using default terminal color)
# you can also use BRIGHT_ and DIM_ prefixes (e.g. BRIGHT_RED) to make the color brighter/darker (note that DIM_ doesn't work in most terminals)
header_color = "BRIGHT_DEFAULT"
even_row_color = "YELLOW"
odd_row_color = "DEFAULT"

date_format = "%Y/%m/%d %H:%M:%S" # more about the format: https://docs.python.org/3/library/datetime.html#strftime-and-strptime-format-codes
# ? at the end means that the column is optional and it's only visible when it contains different values
# .X at the end means that the values of the column are going to be rounded to X decimals
# PDATA:X means that the column is going to be filled with PDATA (player_data) averaged over all games
# PDATA_ALL expands to all PDATA present in the games file (note that ? & .X doesn't work with PDATA_ALL)
leaderboard = "POS,NAME,SCORE.2,GAMES,PERCENTAGE,MU.3,SIGMA.3?,ACTIVE?,ERRORS?,PDATA_ALL,DATE,DESCRIPTION?"

# [RANKING MODEL]
model = "trueskill"
model_draw_prob = 0.0001 # trueskill
model_tau = 0.002 # trueskill / openskill
model_alpha = 0.0001 # global: regularization
model_scale = 3.64 # global: applied linear scale to the result (the goal of default value is to have the similar scale as trueskill)

# [COMMANDS]
# there are special keywords (in the form of %KEYWORD%) that are going to be replaced when the command is executed
dir_bots = "bots" # %DIR% expands to "dir_bots"
# invoked when "psyleague bot add NAME --src SRC" is executed; if no --src is specified, %SRC% = %NAME%
cmd_bot_setup = "g++ -std=c++17 -O2 %SRC%.cpp -o %DIR%/%NAME%.exe && cp %SRC%.cpp %DIR%/%NAME%.cpp" 
# invoked when psyleague needs to play a new match; %P1%, %P2%, ..., %P9% are going to be replaced by the bots' names (generated via matchmaking)
# %ALL_PLAYERS% is a special construct and it's going to be replaced by all players' names separated by a space (this includes anything that was attached to %ALL_PLAYERS%)
cmd_play_game = "python play_game.py %DIR%/%ALL_PLAYERS%.exe" # this is equivalent to "python play_game.py %DIR%/%P1%.exe %DIR%/%P2%.exe" when n_players = 2

# [FILES]
file_log = "psyleague.log"
file_msg = "psyleague.msg"
file_db = "psyleague.db"
file_games = "psyleague.games"
file_lock = "psyleague.lock"
